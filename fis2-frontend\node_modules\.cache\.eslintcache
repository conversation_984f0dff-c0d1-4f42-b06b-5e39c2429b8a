[{"C:\\fis2-web\\fis2-frontend\\src\\index.tsx": "1", "C:\\fis2-web\\fis2-frontend\\src\\reportWebVitals.ts": "2", "C:\\fis2-web\\fis2-frontend\\src\\App.tsx": "3", "C:\\fis2-web\\fis2-frontend\\src\\contexts\\AuthContext.tsx": "4", "C:\\fis2-web\\fis2-frontend\\src\\contexts\\SocketContext.tsx": "5", "C:\\fis2-web\\fis2-frontend\\src\\components\\Layout\\Layout.tsx": "6", "C:\\fis2-web\\fis2-frontend\\src\\components\\ProtectedRoute\\ProtectedRoute.tsx": "7", "C:\\fis2-web\\fis2-frontend\\src\\pages\\Login\\Login.tsx": "8", "C:\\fis2-web\\fis2-frontend\\src\\pages\\Dashboard\\Dashboard.tsx": "9", "C:\\fis2-web\\fis2-frontend\\src\\pages\\Freight\\FreightList.tsx": "10", "C:\\fis2-web\\fis2-frontend\\src\\pages\\Freight\\FreightDetail.tsx": "11", "C:\\fis2-web\\fis2-frontend\\src\\pages\\Tracking\\TrackingPage.tsx": "12", "C:\\fis2-web\\fis2-frontend\\src\\pages\\Reports\\ReportsPage.tsx": "13", "C:\\fis2-web\\fis2-frontend\\src\\services\\authService.ts": "14", "C:\\fis2-web\\fis2-frontend\\src\\services\\freightService.ts": "15"}, {"size": 554, "mtime": 1753970112638, "results": "16", "hashOfConfig": "17"}, {"size": 425, "mtime": 1753970111468, "results": "18", "hashOfConfig": "17"}, {"size": 2866, "mtime": 1753973524886, "results": "19", "hashOfConfig": "17"}, {"size": 2994, "mtime": 1753975110717, "results": "20", "hashOfConfig": "17"}, {"size": 3567, "mtime": 1753975128185, "results": "21", "hashOfConfig": "17"}, {"size": 7151, "mtime": 1753975304389, "results": "22", "hashOfConfig": "17"}, {"size": 2040, "mtime": 1753975315760, "results": "23", "hashOfConfig": "17"}, {"size": 4684, "mtime": 1753975256205, "results": "24", "hashOfConfig": "17"}, {"size": 7798, "mtime": 1754033569322, "results": "25", "hashOfConfig": "17"}, {"size": 706, "mtime": 1753975356635, "results": "26", "hashOfConfig": "17"}, {"size": 826, "mtime": 1753975365967, "results": "27", "hashOfConfig": "17"}, {"size": 708, "mtime": 1753975374039, "results": "28", "hashOfConfig": "17"}, {"size": 725, "mtime": 1753975382941, "results": "29", "hashOfConfig": "17"}, {"size": 3825, "mtime": 1753975144839, "results": "30", "hashOfConfig": "17"}, {"size": 5341, "mtime": 1753975172683, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lld6vm", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\fis2-web\\fis2-frontend\\src\\index.tsx", [], [], "C:\\fis2-web\\fis2-frontend\\src\\reportWebVitals.ts", [], [], "C:\\fis2-web\\fis2-frontend\\src\\App.tsx", [], [], "C:\\fis2-web\\fis2-frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\fis2-web\\fis2-frontend\\src\\contexts\\SocketContext.tsx", ["77"], [], "C:\\fis2-web\\fis2-frontend\\src\\components\\Layout\\Layout.tsx", [], [], "C:\\fis2-web\\fis2-frontend\\src\\components\\ProtectedRoute\\ProtectedRoute.tsx", [], [], "C:\\fis2-web\\fis2-frontend\\src\\pages\\Login\\Login.tsx", [], [], "C:\\fis2-web\\fis2-frontend\\src\\pages\\Dashboard\\Dashboard.tsx", [], [], "C:\\fis2-web\\fis2-frontend\\src\\pages\\Freight\\FreightList.tsx", [], [], "C:\\fis2-web\\fis2-frontend\\src\\pages\\Freight\\FreightDetail.tsx", [], [], "C:\\fis2-web\\fis2-frontend\\src\\pages\\Tracking\\TrackingPage.tsx", [], [], "C:\\fis2-web\\fis2-frontend\\src\\pages\\Reports\\ReportsPage.tsx", [], [], "C:\\fis2-web\\fis2-frontend\\src\\services\\authService.ts", [], [], "C:\\fis2-web\\fis2-frontend\\src\\services\\freightService.ts", [], [], {"ruleId": "78", "severity": 1, "message": "79", "line": 87, "column": 6, "nodeType": "80", "endLine": 87, "endColumn": 30, "suggestions": "81"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'socket'. Either include it or remove the dependency array.", "ArrayExpression", ["82"], {"desc": "83", "fix": "84"}, "Update the dependencies array to be: [isAuthenticated, socket, token]", {"range": "85", "text": "86"}, [2640, 2664], "[isAuthenticated, socket, token]"]