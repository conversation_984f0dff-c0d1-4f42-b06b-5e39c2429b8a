{"name": "@types/multer", "version": "2.0.0", "description": "TypeScript definitions for multer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/multer", "license": "MIT", "contributors": [{"name": "jt000", "githubUsername": "jt000", "url": "https://github.com/jt000"}, {"name": "vili<PERSON><PERSON><PERSON>", "githubUsername": "vilic", "url": "https://github.com/vilic"}, {"name": "<PERSON>-<PERSON>", "githubUsername": "DavidBR-SW", "url": "https://github.com/DavidBR-SW"}, {"name": "<PERSON>", "githubUsername": "mxl", "url": "https://github.com/mxl"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "hyun<PERSON><PERSON>", "url": "https://github.com/hyunseob"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>bas"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/multer"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "f6551dc7472bbda5d8b2e7793797cf9951195b30f235ae1946a50ae4e1f2a7f9", "typeScriptVersion": "5.1"}