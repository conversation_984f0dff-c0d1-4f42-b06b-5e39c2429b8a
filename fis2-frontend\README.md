# FIS2 Web Application

A modern React/TypeScript web application for freight information system management, designed for deployment on Apache web servers with network access capabilities.

## 🚀 Quick Start

### For XAMPP Users (Windows)

1. **Deploy to Apache** (Automated):
   ```cmd
   deploy-to-apache.bat
   ```

2. **Access the application**:
   - Local: `http://localhost/fis2-web`

### For Network Access

1. **Configure for network access**:
   ```cmd
   setup-network-access.bat
   ```

2. **Deploy**:
   ```cmd
   deploy-to-apache.bat
   ```

3. **Access from any device**:
   - `http://YOUR_IP_ADDRESS/fis2-web`

## 📚 Documentation

- **[Quick Setup Guide](QUICK-SETUP.md)** - 5-minute setup instructions
- **[Deployment Guide](DEPLOYMENT.md)** - Comprehensive deployment documentation
- **[Apache Configuration](apache-config/)** - Virtual host configurations

## 🛠️ Development Scripts

### `npm start`

Runs the app in development mode at [http://localhost:3000](http://localhost:3000).

### `npm run build`

Builds the app for production to the `build` folder.

### `npm run build:apache`

Builds the app optimized for Apache deployment (no source maps).

### `npm test`

Launches the test runner in interactive watch mode.

## 🌐 Apache Deployment Features

### ✅ What's Included

- **Client-side routing support** - `.htaccess` configuration for React Router
- **Network access ready** - Access from any device on your local network
- **Automated deployment scripts** - One-click deployment to Apache
- **Cross-platform support** - Windows (XAMPP) and Linux/Mac
- **Production optimized builds** - Minified and optimized for performance
- **Security headers** - Basic security configurations in `.htaccess`
- **Compression enabled** - Gzip compression for better performance

### 📱 Multi-Device Access

The application is configured for access from:
- **Desktop browsers** - Full functionality
- **Mobile devices** - Responsive design
- **Tablets** - Optimized layout
- **Network devices** - Same WiFi network access

## 🔧 Configuration Files

### Environment Configuration
- `.env.production` - Production environment settings
- `.env.local.example` - Template for local development
- `.env.local` - Local development settings (auto-generated)

### Apache Configuration
- `public/.htaccess` - Client-side routing and security headers
- `apache-config/fis2-web.conf` - Virtual host configuration
- `apache-config/xampp-httpd-vhosts.conf` - XAMPP-specific configuration

### Deployment Scripts
- `deploy-to-apache.bat` - Windows deployment script
- `deploy-to-apache.sh` - Linux/Mac deployment script
- `setup-network-access.bat` - Network configuration helper
- `test-deployment.bat` - Deployment validation script

## 🧪 Testing Your Deployment

### Automated Testing
```cmd
test-deployment.bat
```

### Manual Testing Checklist
- [ ] Application loads at `http://localhost/fis2-web`
- [ ] Navigation works between all pages
- [ ] Page refresh works on any route
- [ ] No console errors in browser (F12)
- [ ] Mobile responsive design works
- [ ] Network access works from other devices
- [ ] API calls succeed (backend must be running)

## 🔍 Troubleshooting

### Common Issues

**404 on page refresh**: Ensure `.htaccess` is deployed and mod_rewrite is enabled

**API calls fail**: Check backend is running and `REACT_APP_API_URL` is correct

**Can't access from network**: Check firewall settings and IP configuration

**Blank page**: Check browser console for errors and verify all files deployed

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed troubleshooting.

## 📋 Prerequisites

- **Node.js** v16+ and npm
- **Apache Web Server** or **XAMPP**
- **Backend API** running on port 5000

## 🏗️ Architecture

- **Frontend**: React 19 + TypeScript + Material-UI v7
- **Routing**: React Router v7 with client-side routing
- **State Management**: React Context API
- **HTTP Client**: Axios
- **Real-time**: Socket.IO client
- **Build Tool**: Create React App with custom configurations

## 📄 License

This project is part of the FIS2 system for freight management.

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can’t go back!**

If you aren’t satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you’re on your own.

You don’t have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn’t feel obligated to use this feature. However we understand that this tool wouldn’t be useful if you couldn’t customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).
