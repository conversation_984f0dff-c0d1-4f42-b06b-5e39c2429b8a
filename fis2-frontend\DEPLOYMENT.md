# FIS2 Web Application - Apache Deployment Guide

This guide provides comprehensive instructions for deploying the FIS2 React/TypeScript web application on an Apache web server (including XAMPP) for local development and testing, with network access from multiple devices.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Quick Deployment](#quick-deployment)
3. [Manual Deployment](#manual-deployment)
4. [Apache Configuration](#apache-configuration)
5. [Network Access Setup](#network-access-setup)
6. [Backend Configuration](#backend-configuration)
7. [Testing](#testing)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### Software Requirements

- **Node.js** (v16 or higher) - for building the React application
- **npm** - comes with Node.js
- **Apache Web Server** or **XAMPP** (recommended for Windows)
- **Git** (optional, for version control)

### XAMPP Installation (Windows)

1. Download XAMPP from [https://www.apachefriends.org/](https://www.apachefriends.org/)
2. Install XAMPP to `C:\xampp` (default location)
3. Start Apache from the XAMPP Control Panel

### Apache Installation (Linux/Mac)

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install apache2

# CentOS/RHEL
sudo yum install httpd

# macOS (using Homebrew)
brew install httpd
```

## Quick Deployment

### Using Deployment Scripts

#### Windows (XAMPP)

1. Open Command Prompt as Administrator
2. Navigate to the `fis2-frontend` directory
3. Run the deployment script:

```cmd
deploy-to-apache.bat
```

Or specify a custom Apache directory:

```cmd
deploy-to-apache.bat "C:\custom\path\to\apache\htdocs\fis2-web"
```

#### Linux/Mac

1. Open Terminal
2. Navigate to the `fis2-frontend` directory
3. Make the script executable and run it:

```bash
chmod +x deploy-to-apache.sh
./deploy-to-apache.sh
```

Or specify a custom Apache directory:

```bash
./deploy-to-apache.sh "/var/www/html/fis2-web"
```

## Manual Deployment

### Step 1: Build the Application

1. Navigate to the `fis2-frontend` directory
2. Install dependencies (if not already done):

```bash
npm install
```

3. Build the application for production:

```bash
npm run build:apache
```

This creates a `build` folder with optimized production files.

### Step 2: Copy Files to Apache

#### Windows (XAMPP)

Copy the contents of the `build` folder to:
```
C:\xampp\htdocs\fis2-web\
```

#### Linux

Copy the contents of the `build` folder to:
```
/var/www/html/fis2-web/
```

Set proper permissions:
```bash
sudo chown -R www-data:www-data /var/www/html/fis2-web
sudo chmod -R 755 /var/www/html/fis2-web
```

## Apache Configuration

### Basic Configuration (Simple Setup)

For basic setup, just ensure Apache is running and the files are in the correct directory. The included `.htaccess` file handles client-side routing.

### Advanced Configuration (Virtual Hosts)

For better organization and network access, configure virtual hosts:

#### Windows (XAMPP)

1. Edit `C:\xampp\apache\conf\extra\httpd-vhosts.conf`
2. Add the content from `apache-config/xampp-httpd-vhosts.conf`
3. Edit `C:\xampp\apache\conf\httpd.conf` and uncomment:
   ```
   Include conf/extra/httpd-vhosts.conf
   ```
4. Add to `C:\Windows\System32\drivers\etc\hosts`:
   ```
   127.0.0.1    fis2-web.local
   ```
5. Restart Apache

#### Linux

1. Create a new virtual host file:
   ```bash
   sudo nano /etc/apache2/sites-available/fis2-web.conf
   ```
2. Add the content from `apache-config/fis2-web.conf`
3. Enable the site:
   ```bash
   sudo a2ensite fis2-web.conf
   sudo systemctl reload apache2
   ```
4. Add to `/etc/hosts`:
   ```
   127.0.0.1    fis2-web.local
   ```

## Network Access Setup

To access the application from other devices on your local network:

### Step 1: Find Your IP Address

#### Windows
```cmd
ipconfig
```
Look for "IPv4 Address" under your active network adapter.

#### Linux/Mac
```bash
ifconfig
# or
ip addr show
```

### Step 2: Configure Firewall

#### Windows
1. Open Windows Defender Firewall
2. Allow Apache through the firewall
3. Or temporarily disable the firewall for testing

#### Linux
```bash
# Ubuntu/Debian
sudo ufw allow 80/tcp

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --reload
```

### Step 3: Update Virtual Host Configuration

Replace `*************` in the virtual host configuration files with your actual IP address.

### Step 4: Access from Other Devices

From other devices on the same network, access:
- `http://YOUR_IP_ADDRESS/fis2-web` (basic setup)
- `http://YOUR_IP_ADDRESS` (if using virtual host with IP)

Example: `http://*************/fis2-web`

## Backend Configuration

The React application needs to communicate with the backend API. Update the API URL:

### Option 1: Environment Variables

Create `.env.local` in the `fis2-frontend` directory:

```env
REACT_APP_API_URL=http://YOUR_IP_ADDRESS:5000
```

Replace `YOUR_IP_ADDRESS` with your actual IP address.

### Option 2: Production Environment File

Edit `.env.production`:

```env
REACT_APP_API_URL=http://YOUR_IP_ADDRESS:5000
```

### Rebuild After Configuration Changes

After changing environment variables, rebuild the application:

```bash
npm run build:apache
```

Then redeploy the files to Apache.

## Testing

### Local Testing

1. Open a web browser
2. Navigate to:
   - `http://localhost/fis2-web` (basic setup)
   - `http://fis2-web.local` (virtual host setup)

### Network Testing

1. From another device on the same network
2. Navigate to:
   - `http://YOUR_IP_ADDRESS/fis2-web`
   - `http://YOUR_IP_ADDRESS` (if using IP-based virtual host)

### Test Checklist

- [ ] Application loads without errors
- [ ] Client-side routing works (refresh on any page)
- [ ] API calls work (check browser console for errors)
- [ ] Responsive design works on mobile devices
- [ ] All static assets load correctly

## Troubleshooting

### Common Issues

#### 1. 404 Error on Page Refresh

**Problem**: React Router routes return 404 when refreshed.

**Solution**: Ensure `.htaccess` file is in the Apache document root and mod_rewrite is enabled.

#### 2. API Calls Fail

**Problem**: Network requests to backend fail.

**Solutions**:
- Check if backend server is running
- Verify `REACT_APP_API_URL` is correct
- Check firewall settings
- Ensure CORS is configured on backend

#### 3. Cannot Access from Other Devices

**Problem**: Application not accessible from network devices.

**Solutions**:
- Check firewall settings
- Verify IP address is correct
- Ensure Apache is listening on all interfaces
- Check virtual host configuration

#### 4. Static Files Not Loading

**Problem**: CSS, JS, or images not loading.

**Solutions**:
- Check file permissions
- Verify Apache configuration
- Check browser console for 404 errors
- Ensure `homepage: "."` is set in package.json

### Enable Apache Modules

If `.htaccess` rules don't work, ensure required modules are enabled:

#### Windows (XAMPP)
Edit `C:\xampp\apache\conf\httpd.conf` and uncomment:
```
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule headers_module modules/mod_headers.so
```

#### Linux
```bash
sudo a2enmod rewrite
sudo a2enmod headers
sudo systemctl restart apache2
```

### Logs

Check Apache error logs for issues:

#### Windows (XAMPP)
```
C:\xampp\apache\logs\error.log
```

#### Linux
```bash
sudo tail -f /var/log/apache2/error.log
```

## Additional Resources

- [Apache Virtual Host Documentation](https://httpd.apache.org/docs/2.4/vhosts/)
- [React Router Documentation](https://reactrouter.com/)
- [Create React App Deployment](https://create-react-app.dev/docs/deployment/)
- [XAMPP Documentation](https://www.apachefriends.org/docs/)

## Support

For issues specific to this deployment:

1. Check the troubleshooting section above
2. Verify all prerequisites are met
3. Check Apache and application logs
4. Test with a simple HTML file first to ensure Apache is working

---

**Note**: This deployment guide is for development and testing purposes. For production deployment, additional security measures and optimizations should be implemented.
