#!/bin/bash
# Deployment script for FIS2 Web Application to Apache
# This script builds the React application and copies it to Apache document root

echo "========================================"
echo "FIS2 Web Application - Apache Deployment"
echo "========================================"
echo

# Check if we're in the correct directory
if [ ! -f "package.json" ]; then
    echo "Error: package.json not found. Please run this script from the fis2-frontend directory."
    exit 1
fi

# Set default Apache document root (modify as needed)
APACHE_ROOT="/var/www/html/fis2-web"

# Allow user to specify custom Apache root
if [ ! -z "$1" ]; then
    APACHE_ROOT="$1"
fi

echo "Building React application for production..."
echo

# Build the application
npm run build:apache
if [ $? -ne 0 ]; then
    echo "Error: Build failed. Please check the error messages above."
    exit 1
fi

echo
echo "Build completed successfully!"
echo

# Create Apache directory if it doesn't exist
if [ ! -d "$APACHE_ROOT" ]; then
    echo "Creating Apache directory: $APACHE_ROOT"
    sudo mkdir -p "$APACHE_ROOT"
fi

# Copy build files to Apache document root
echo "Copying files to Apache document root: $APACHE_ROOT"
echo

# Remove old files (optional - comment out if you want to keep them)
if [ "$(ls -A $APACHE_ROOT 2>/dev/null)" ]; then
    echo "Removing old files..."
    sudo rm -rf "$APACHE_ROOT"/*
fi

# Copy new files
sudo cp -r build/* "$APACHE_ROOT/"
if [ $? -ne 0 ]; then
    echo "Error: Failed to copy files to Apache directory."
    exit 1
fi

# Set proper permissions
sudo chown -R www-data:www-data "$APACHE_ROOT"
sudo chmod -R 755 "$APACHE_ROOT"

echo
echo "========================================"
echo "Deployment completed successfully!"
echo "========================================"
echo
echo "Application deployed to: $APACHE_ROOT"
echo
echo "Next steps:"
echo "1. Make sure Apache is running"
echo "2. Configure virtual hosts (see apache-config folder)"
echo "3. Access the application at: http://localhost/fis2-web"
echo "   or http://your-ip-address/fis2-web for network access"
echo
echo "For detailed setup instructions, see DEPLOYMENT.md"
echo
