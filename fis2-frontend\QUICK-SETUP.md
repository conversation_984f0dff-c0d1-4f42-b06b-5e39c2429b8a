# FIS2 Web Application - Quick Setup Guide

## 🚀 Quick Start (5 Minutes)

### For Windows Users with XAMPP

1. **Install XAMPP** (if not already installed)
   - Download from [https://www.apachefriends.org/](https://www.apachefriends.org/)
   - Install to default location (`C:\xampp`)

2. **Start Apache**
   - Open XAMPP Control Panel
   - Click "Start" next to Apache

3. **Deploy the Application**
   - Open Command Prompt as Administrator
   - Navigate to the `fis2-frontend` folder
   - Run: `deploy-to-apache.bat`

4. **Access the Application**
   - Open browser and go to: `http://localhost/fis2-web`

### For Network Access (Access from Phone/Tablet)

1. **Find Your IP Address**
   - Open Command Prompt
   - Run: `ipconfig`
   - Note your IPv4 Address (e.g., *************)

2. **Update API Configuration**
   - Copy `.env.local.example` to `.env.local`
   - Edit `.env.local` and replace `localhost` with your IP:
     ```
     REACT_APP_API_URL=http://*************:5000
     ```

3. **Rebuild and Deploy**
   - Run: `deploy-to-apache.bat`

4. **Configure Firewall**
   - Allow Apache through Windows Firewall
   - Or temporarily disable firewall for testing

5. **Access from Other Devices**
   - From phone/tablet browser: `http://*************/fis2-web`

## 🔧 Backend Setup

The frontend needs the backend API running. Make sure to:

1. **Start the Backend Server**
   - Navigate to `fis2-backend` folder
   - Run: `npm install` (first time only)
   - Run: `npm start`
   - Backend should be running on port 5000

2. **Verify Backend is Running**
   - Open browser: `http://localhost:5000/api/health`
   - Should show: `{"status":"OK",...}`

## 📱 Testing on Mobile Devices

1. **Connect to Same WiFi Network**
   - Ensure your phone/tablet is on the same WiFi as your computer

2. **Access the Application**
   - Open browser on mobile device
   - Go to: `http://YOUR_IP_ADDRESS/fis2-web`
   - Replace `YOUR_IP_ADDRESS` with your actual IP

3. **Test Features**
   - Login functionality
   - Navigation between pages
   - Responsive design

## ⚠️ Common Issues & Quick Fixes

### Issue: "Cannot GET /" or 404 errors
**Fix**: Make sure `.htaccess` file is in the Apache folder and mod_rewrite is enabled.

### Issue: API calls fail
**Fix**: 
- Check if backend is running (`http://localhost:5000/api/health`)
- Verify `REACT_APP_API_URL` in `.env.local`
- Check firewall settings

### Issue: Can't access from other devices
**Fix**:
- Check Windows Firewall settings
- Verify IP address is correct
- Make sure Apache is running

### Issue: Page is blank or white screen
**Fix**:
- Check browser console for errors (F12)
- Verify all files copied correctly
- Check Apache error logs

## 📁 File Structure After Deployment

```
C:\xampp\htdocs\fis2-web\
├── index.html          # Main HTML file
├── .htaccess           # Apache configuration
├── static\             # CSS, JS, and other assets
│   ├── css\
│   └── js\
├── favicon.ico
└── manifest.json
```

## 🔍 Verification Steps

1. **Check Apache Status**
   - XAMPP Control Panel shows Apache as "Running"

2. **Check Files**
   - `C:\xampp\htdocs\fis2-web\` contains built files
   - `.htaccess` file is present

3. **Check Network**
   - Can access `http://localhost/fis2-web`
   - Can access from other devices using IP address

4. **Check Backend**
   - Backend API is running on port 5000
   - API health check responds correctly

## 📞 Need Help?

1. **Check the detailed guide**: `DEPLOYMENT.md`
2. **Check Apache logs**: `C:\xampp\apache\logs\error.log`
3. **Check browser console**: Press F12 and look for errors
4. **Verify prerequisites**: Node.js, npm, Apache/XAMPP installed

---

**Estimated Setup Time**: 5-10 minutes for basic setup, 15-20 minutes for network access configuration.
