@echo off
REM Deployment script for FIS2 Web Application to Apache/XAMPP
REM This script builds the React application and copies it to Apache document root

echo ========================================
echo FIS2 Web Application - Apache Deployment
echo ========================================
echo.

REM Check if we're in the correct directory
if not exist "package.json" (
    echo Error: package.json not found. Please run this script from the fis2-frontend directory.
    pause
    exit /b 1
)

REM Set default Apache document root (modify as needed)
set APACHE_ROOT=C:\xampp\htdocs\fis2-web

REM Allow user to specify custom Apache root
if not "%1"=="" (
    set APACHE_ROOT=%1
)

echo Building React application for production...
echo.

REM Build the application
call npm run build:apache
if errorlevel 1 (
    echo Error: Build failed. Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.

REM Create Apache directory if it doesn't exist
if not exist "%APACHE_ROOT%" (
    echo Creating Apache directory: %APACHE_ROOT%
    mkdir "%APACHE_ROOT%"
)

REM Copy build files to Apache document root
echo Copying files to Apache document root: %APACHE_ROOT%
echo.

REM Remove old files (optional - comment out if you want to keep them)
if exist "%APACHE_ROOT%\*" (
    echo Removing old files...
    del /q "%APACHE_ROOT%\*"
    for /d %%x in ("%APACHE_ROOT%\*") do rd /s /q "%%x"
)

REM Copy new files
xcopy "build\*" "%APACHE_ROOT%\" /E /I /Y
if errorlevel 1 (
    echo Error: Failed to copy files to Apache directory.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Deployment completed successfully!
echo ========================================
echo.
echo Application deployed to: %APACHE_ROOT%
echo.
echo Next steps:
echo 1. Make sure Apache/XAMPP is running
echo 2. Configure virtual hosts (see apache-config folder)
echo 3. Access the application at: http://localhost/fis2-web
echo    or http://your-ip-address/fis2-web for network access
echo.
echo For detailed setup instructions, see DEPLOYMENT.md
echo.
pause
