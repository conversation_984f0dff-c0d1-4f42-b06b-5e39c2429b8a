{"ast": null, "code": "import React,{useState,useEffect}from'react';import{<PERSON><PERSON>,Card,CardContent,Typography,Box,CircularProgress,Alert,Paper,List,ListItem,ListItemButton,ListItemText,Chip,IconButton,Tooltip}from'@mui/material';import{LocalShipping,TrendingUp,Schedule,CheckCircle,Refresh,Warning}from'@mui/icons-material';import{freightService}from'../../services/freightService';import{useAuth}from'../../contexts/AuthContext';import{useNavigate}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Dashboard=()=>{const[summary,setSummary]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[lastUpdated,setLastUpdated]=useState(\"\");const{user}=useAuth();const navigate=useNavigate();const loadDashboardData=async()=>{try{setLoading(true);setError(null);const data=await freightService.getDashboardSummary();setSummary(data);setLastUpdated(new Date().toLocaleString());}catch(err){setError(err.message||'Failed to load dashboard data');}finally{setLoading(false);}};useEffect(()=>{loadDashboardData();// eslint-disable-next-line\n},[]);const StatCard=_ref=>{let{title,value,icon,color,subtitle}=_ref;return/*#__PURE__*/_jsx(Card,{elevation:2,children:/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{color:\"textSecondary\",gutterBottom:true,variant:\"body2\",children:title}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"div\",color:color,children:value}),subtitle&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:subtitle})]}),/*#__PURE__*/_jsx(Box,{color:color,children:icon})]})})});};if(loading){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"400px\",children:/*#__PURE__*/_jsx(CircularProgress,{})});}if(error){return/*#__PURE__*/_jsx(Alert,{severity:\"error\",action:/*#__PURE__*/_jsx(IconButton,{color:\"inherit\",size:\"small\",onClick:loadDashboardData,children:/*#__PURE__*/_jsx(Refresh,{})}),children:error});}return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",mb:3,children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h4\",component:\"h1\",children:[\"Welcome back, \",(user===null||user===void 0?void 0:user.name)||(user===null||user===void 0?void 0:user.username)||\"User\",\"!\"]}),/*#__PURE__*/_jsx(Tooltip,{title:\"Refresh Dashboard\",children:/*#__PURE__*/_jsx(IconButton,{onClick:loadDashboardData,disabled:loading,children:/*#__PURE__*/_jsx(Refresh,{})})})]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6,md:3},children:/*#__PURE__*/_jsx(StatCard,{title:\"Total Shipments\",value:(summary===null||summary===void 0?void 0:summary.totalShipments)||0,icon:/*#__PURE__*/_jsx(LocalShipping,{fontSize:\"large\"}),color:\"primary.main\"})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6,md:3},children:/*#__PURE__*/_jsx(StatCard,{title:\"Active Shipments\",value:(summary===null||summary===void 0?void 0:summary.activeShipments)||0,icon:/*#__PURE__*/_jsx(Schedule,{fontSize:\"large\"}),color:\"warning.main\"})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6,md:3},children:/*#__PURE__*/_jsx(StatCard,{title:\"Completed\",value:(summary===null||summary===void 0?void 0:summary.completedShipments)||0,icon:/*#__PURE__*/_jsx(CheckCircle,{fontSize:\"large\"}),color:\"success.main\"})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,sm:6,md:3},children:/*#__PURE__*/_jsx(StatCard,{title:\"Total Value\",value:`$${((summary===null||summary===void 0?void 0:summary.totalValue)||0).toLocaleString()}`,icon:/*#__PURE__*/_jsx(TrendingUp,{fontSize:\"large\"}),color:\"info.main\"})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,md:6},children:/*#__PURE__*/_jsx(Card,{elevation:2,children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Performance Metrics\"}),/*#__PURE__*/_jsxs(List,{children:[/*#__PURE__*/_jsxs(ListItem,{children:[/*#__PURE__*/_jsx(ListItemText,{primary:\"Average Transit Time\",secondary:`${(summary===null||summary===void 0?void 0:summary.averageTransitTime)||0} days`}),/*#__PURE__*/_jsx(Chip,{label:\"Good\",color:\"success\",size:\"small\"})]}),/*#__PURE__*/_jsxs(ListItem,{children:[/*#__PURE__*/_jsx(ListItemText,{primary:\"On-Time Delivery Rate\",secondary:`${(summary===null||summary===void 0?void 0:summary.onTimeDeliveryRate)||0}%`}),/*#__PURE__*/_jsx(Chip,{label:summary&&summary.onTimeDeliveryRate>=90?\"Excellent\":\"Good\",color:summary&&summary.onTimeDeliveryRate>=90?\"success\":\"warning\",size:\"small\"})]}),/*#__PURE__*/_jsxs(ListItem,{children:[/*#__PURE__*/_jsx(ListItemText,{primary:\"Pending Shipments\",secondary:`${(summary===null||summary===void 0?void 0:summary.pendingShipments)||0} awaiting confirmation`}),summary&&summary.pendingShipments>0&&/*#__PURE__*/_jsx(Warning,{color:\"warning\"})]})]})]})})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12,md:6},children:/*#__PURE__*/_jsx(Card,{elevation:2,children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Quick Actions\"}),/*#__PURE__*/_jsxs(List,{children:[/*#__PURE__*/_jsx(ListItem,{disablePadding:true,children:/*#__PURE__*/_jsx(ListItemButton,{onClick:()=>navigate('/freight'),children:/*#__PURE__*/_jsx(ListItemText,{primary:\"View All Shipments\",secondary:\"Manage and track freight shipments\"})})}),/*#__PURE__*/_jsx(ListItem,{disablePadding:true,children:/*#__PURE__*/_jsx(ListItemButton,{onClick:()=>navigate('/tracking'),children:/*#__PURE__*/_jsx(ListItemText,{primary:\"Track Shipment\",secondary:\"Search and track by booking number\"})})}),/*#__PURE__*/_jsx(ListItem,{disablePadding:true,children:/*#__PURE__*/_jsx(ListItemButton,{onClick:()=>navigate('/reports'),children:/*#__PURE__*/_jsx(ListItemText,{primary:\"Generate Reports\",secondary:\"View performance and financial reports\"})})})]})]})})}),/*#__PURE__*/_jsx(Grid,{size:{xs:12},children:/*#__PURE__*/_jsx(Paper,{elevation:1,sx:{p:2,bgcolor:'info.light',color:'info.contrastText'},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"System Status:\"}),\" All systems operational \\u2022 Connected to FIS2 servers \\u2022 Last updated: \",lastUpdated]})})})]})]});};export default Dashboard;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}