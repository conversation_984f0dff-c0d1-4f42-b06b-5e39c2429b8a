import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  LocalShipping,
  TrendingUp,
  Schedule,
  CheckCircle,
  Refresh,
  Warning,
} from '@mui/icons-material';
import { freightService, DashboardSummary } from '../../services/freightService';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const Dashboard: React.FC = () => {
  const [summary, setSummary] = useState<DashboardSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string>("");
  const { user } = useAuth();
  const navigate = useNavigate();

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await freightService.getDashboardSummary();
      setSummary(data);
      setLastUpdated(new Date().toLocaleString());
    } catch (err: any) {
      setError(err.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
    // eslint-disable-next-line
  }, []);

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
    subtitle?: string;
  }> = ({ title, value, icon, color, subtitle }) => (
    <Card elevation={2}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={color}>
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="textSecondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box color={color}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert 
        severity="error" 
        action={
          <IconButton color="inherit" size="small" onClick={loadDashboardData}>
            <Refresh />
          </IconButton>
        }
      >
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Welcome back, {user?.name || user?.username || "User"}!
        </Typography>
        <Tooltip title="Refresh Dashboard">
          <IconButton onClick={loadDashboardData} disabled={loading}>
            <Refresh />
          </IconButton>
        </Tooltip>
      </Box>

      <Grid container spacing={3}>
        {/* Summary Cards */}
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Total Shipments"
            value={summary?.totalShipments || 0}
            icon={<LocalShipping fontSize="large" />}
            color="primary.main"
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Active Shipments"
            value={summary?.activeShipments || 0}
            icon={<Schedule fontSize="large" />}
            color="warning.main"
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Completed"
            value={summary?.completedShipments || 0}
            icon={<CheckCircle fontSize="large" />}
            color="success.main"
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Total Value"
            value={`$${(summary?.totalValue || 0).toLocaleString()}`}
            icon={<TrendingUp fontSize="large" />}
            color="info.main"
          />
        </Grid>

        {/* Performance Metrics */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Performance Metrics
              </Typography>
              <List>
                <ListItem>
                  <ListItemText
                    primary="Average Transit Time"
                    secondary={`${summary?.averageTransitTime || 0} days`}
                  />
                  <Chip 
                    label="Good" 
                    color="success" 
                    size="small" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="On-Time Delivery Rate"
                    secondary={`${summary?.onTimeDeliveryRate || 0}%`}
                  />
                  <Chip 
                    label={summary && summary.onTimeDeliveryRate >= 90 ? "Excellent" : "Good"} 
                    color={summary && summary.onTimeDeliveryRate >= 90 ? "success" : "warning"} 
                    size="small" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Pending Shipments"
                    secondary={`${summary?.pendingShipments || 0} awaiting confirmation`}
                  />
                  {summary && summary.pendingShipments > 0 && (
                    <Warning color="warning" />
                  )}
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <List>
                <ListItem disablePadding>
                  <ListItemButton onClick={() => navigate('/freight')}>
                    <ListItemText
                      primary="View All Shipments"
                      secondary="Manage and track freight shipments"
                    />
                  </ListItemButton>
                </ListItem>
                <ListItem disablePadding>
                  <ListItemButton onClick={() => navigate('/tracking')}>
                    <ListItemText
                      primary="Track Shipment"
                      secondary="Search and track by booking number"
                    />
                  </ListItemButton>
                </ListItem>
                <ListItem disablePadding>
                  <ListItemButton onClick={() => navigate('/reports')}>
                    <ListItemText
                      primary="Generate Reports"
                      secondary="View performance and financial reports"
                    />
                  </ListItemButton>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* System Status */}
        <Grid size={{ xs: 12 }}>
          <Paper elevation={1} sx={{ p: 2, bgcolor: 'info.light', color: 'info.contrastText' }}>
            <Typography variant="body2">
              <strong>System Status:</strong> All systems operational • 
              Connected to FIS2 servers • 
              Last updated: {lastUpdated}
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
