@echo off
REM Network Access Configuration Script for FIS2 Web Application
REM This script helps configure the application for network access

echo ========================================
echo FIS2 Web - Network Access Configuration
echo ========================================
echo.

REM Get the local IP address
echo Detecting your IP address...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set IP=%%a
    goto :found_ip
)

:found_ip
REM Remove leading spaces
for /f "tokens=* delims= " %%a in ("%IP%") do set IP=%%a

if "%IP%"=="" (
    echo Error: Could not detect IP address automatically.
    echo Please run 'ipconfig' manually to find your IPv4 address.
    pause
    exit /b 1
)

echo.
echo Your IP address is: %IP%
echo.

REM Check if .env.local exists
if exist ".env.local" (
    echo Found existing .env.local file.
    echo Creating backup as .env.local.backup
    copy ".env.local" ".env.local.backup" >nul
)

REM Create .env.local with the detected IP
echo Creating .env.local with network configuration...
echo # Local development environment configuration > .env.local
echo # Generated automatically by setup-network-access.bat >> .env.local
echo. >> .env.local
echo # API Configuration for network access >> .env.local
echo REACT_APP_API_URL=http://%IP%:5000 >> .env.local
echo. >> .env.local
echo # Development Configuration >> .env.local
echo GENERATE_SOURCEMAP=true >> .env.local
echo REACT_APP_VERSION=1.0.0-dev >> .env.local
echo REACT_APP_ENVIRONMENT=development >> .env.local

echo.
echo Configuration created successfully!
echo.
echo Next steps:
echo 1. Make sure your backend server is running on port 5000
echo 2. Run the deployment script: deploy-to-apache.bat
echo 3. Configure Windows Firewall to allow Apache
echo 4. Access from other devices: http://%IP%/fis2-web
echo.

REM Ask if user wants to configure firewall
echo Would you like to add a Windows Firewall rule for Apache? (y/n)
set /p FIREWALL_CHOICE=

if /i "%FIREWALL_CHOICE%"=="y" (
    echo.
    echo Adding Windows Firewall rule for Apache...
    netsh advfirewall firewall add rule name="Apache HTTP" dir=in action=allow protocol=TCP localport=80
    if errorlevel 1 (
        echo Warning: Failed to add firewall rule. You may need to run as Administrator.
        echo Please manually allow Apache through Windows Firewall.
    ) else (
        echo Firewall rule added successfully!
    )
)

echo.
echo ========================================
echo Network Configuration Complete!
echo ========================================
echo.
echo Your application will be accessible at:
echo - Local: http://localhost/fis2-web
echo - Network: http://%IP%/fis2-web
echo.
echo Backend API should be accessible at:
echo - http://%IP%:5000/api/health
echo.
echo Don't forget to:
echo 1. Start your backend server (in fis2-backend folder: npm start)
echo 2. Deploy the application (run: deploy-to-apache.bat)
echo 3. Test from another device on the same network
echo.
pause
