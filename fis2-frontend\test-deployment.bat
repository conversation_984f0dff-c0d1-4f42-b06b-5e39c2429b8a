@echo off
REM Deployment Testing Script for FIS2 Web Application
REM This script performs basic validation of the Apache deployment

echo ========================================
echo FIS2 Web Application - Deployment Test
echo ========================================
echo.

REM Set default Apache document root
set APACHE_ROOT=C:\xampp\htdocs\fis2-web

REM Allow user to specify custom Apache root
if not "%1"=="" (
    set APACHE_ROOT=%1
)

echo Testing deployment at: %APACHE_ROOT%
echo.

REM Test 1: Check if Apache directory exists
echo [1/7] Checking Apache directory...
if exist "%APACHE_ROOT%" (
    echo ✓ Apache directory exists: %APACHE_ROOT%
) else (
    echo ✗ Apache directory not found: %APACHE_ROOT%
    echo Please run deploy-to-apache.bat first
    goto :end_tests
)

REM Test 2: Check if index.html exists
echo [2/7] Checking main HTML file...
if exist "%APACHE_ROOT%\index.html" (
    echo ✓ index.html found
) else (
    echo ✗ index.html not found
    echo Deployment may have failed
    goto :end_tests
)

REM Test 3: Check if .htaccess exists
echo [3/7] Checking .htaccess file...
if exist "%APACHE_ROOT%\.htaccess" (
    echo ✓ .htaccess file found
) else (
    echo ✗ .htaccess file not found
    echo Client-side routing may not work properly
)

REM Test 4: Check if static assets exist
echo [4/7] Checking static assets...
if exist "%APACHE_ROOT%\static" (
    echo ✓ Static assets directory found
) else (
    echo ✗ Static assets directory not found
    echo CSS and JavaScript may not load
)

REM Test 5: Check if XAMPP Apache is running
echo [5/7] Checking if Apache is running...
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✓ Apache process is running
) else (
    echo ✗ Apache process not found
    echo Please start Apache from XAMPP Control Panel
)

REM Test 6: Test local HTTP access
echo [6/7] Testing local HTTP access...
curl -s -o nul -w "%%{http_code}" http://localhost/fis2-web/ > temp_status.txt 2>nul
if exist temp_status.txt (
    set /p HTTP_STATUS=<temp_status.txt
    del temp_status.txt
    if "!HTTP_STATUS!"=="200" (
        echo ✓ Local HTTP access successful (Status: !HTTP_STATUS!)
    ) else (
        echo ✗ Local HTTP access failed (Status: !HTTP_STATUS!)
        echo Check Apache configuration and virtual hosts
    )
) else (
    echo ? Could not test HTTP access (curl not available)
    echo Please test manually: http://localhost/fis2-web
)

REM Test 7: Get IP address for network testing
echo [7/7] Network access information...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set IP=%%a
    goto :found_ip_test
)

:found_ip_test
REM Remove leading spaces
for /f "tokens=* delims= " %%a in ("%IP%") do set IP=%%a

if not "%IP%"=="" (
    echo ✓ Network IP detected: %IP%
    echo   Test from other devices: http://%IP%/fis2-web
) else (
    echo ? Could not detect IP address
    echo   Run 'ipconfig' to find your IPv4 address
)

:end_tests
echo.
echo ========================================
echo Test Summary
echo ========================================
echo.

REM Check if backend is running
echo Additional checks:
echo.
echo Backend API Status:
curl -s http://localhost:5000/api/health > nul 2>&1
if errorlevel 1 (
    echo ✗ Backend API not responding at http://localhost:5000
    echo   Start backend: cd fis2-backend && npm start
) else (
    echo ✓ Backend API is responding
)

echo.
echo Manual Testing Checklist:
echo □ Open http://localhost/fis2-web in browser
echo □ Verify application loads without errors
echo □ Test navigation between pages
echo □ Refresh page on different routes (tests .htaccess)
echo □ Check browser console for errors (F12)
echo □ Test login functionality
echo □ Test from mobile device on same network
echo.

if not "%IP%"=="" (
    echo Network Testing:
    echo □ Test from another device: http://%IP%/fis2-web
    echo □ Verify responsive design on mobile
    echo □ Check API calls work from network devices
    echo.
)

echo For detailed troubleshooting, see DEPLOYMENT.md
echo.
pause
